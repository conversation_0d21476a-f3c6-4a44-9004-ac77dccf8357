@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 210 11.1% 3.53%;

    --card: 0 0% 100%;
    --card-foreground: 210 11.1% 3.53%;

    --popover: 0 0% 100%;
    --popover-foreground: 210 11.1% 3.53%;

    --primary: 0 0% 9%;
    --primary-foreground: 0 0% 98%;

    --secondary: 0 0% 96.1%;
    --secondary-foreground: 0 0% 9%;

    --muted: 0 0% 96.1%;
    --muted-foreground: 0 0% 45.1%;

    --accent: 0 0% 96.1%;
    --accent-foreground: 0 0% 9%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;

    --border: 0 0% 89.8%;
    --input: 0 0% 89.8%;
    --ring: 0 0% 3.9%;

    --radius: 0.5rem;
  }

  .dark {
    --background: 210 11.1% 3.53%;
    --foreground: 0 0% 95%;

    --card: 210 11.1% 3.53%;
    --card-foreground: 0 0% 92%;

    --popover: 210 11.1% 3.53%;
    --popover-foreground: 0 0% 98%;

    --primary: 0 0% 98%;
    --primary-foreground: 0 0% 9%;

    --secondary: 0 0% 14.9%;
    --secondary-foreground: 0 0% 98%;

    --muted: 0 0% 14.9%;
    --muted-foreground: 0 0% 85%;

    --accent: 0 0% 14.9%;
    --accent-foreground: 0 0% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;

    --border: 0 0% 14.9%;
    --input: 0 0% 14.9%;
    --ring: 0 0% 83.1%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply text-foreground;
    background: linear-gradient(
      -45deg,
      #0f1419,
      #1a2332,
      #0d1b2a,
      #162447,
      #1e3a5f,
      #0a1628
    );
    background-size: 400% 400%;
    animation: gradientShift 15s ease infinite;
    min-height: 100vh;
  }

  .dark body {
    background: linear-gradient(
      -45deg,
      #050a0f,
      #0a1117,
      #061018,
      #0c1a2e,
      #112240,
      #030810
    );
    background-size: 400% 400%;
    animation: gradientShift 15s ease infinite;
  }
}

@keyframes gradientShift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

/* Glass effect for cards */
.glass-card {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
}

.dark .glass-card {
  background: rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Override card backgrounds to be transparent */
[data-theme] .bg-card,
.bg-card {
  background: rgba(255, 255, 255, 0.1) !important;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.dark [data-theme] .bg-card,
.dark .bg-card {
  background: rgba(0, 0, 0, 0.2) !important;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

h3 code {
  @apply !text-lg md:!text-xl;
}

pre {
  @apply !px-0 rounded-lg overflow-x-auto py-4;
}

pre [data-line] {
  @apply px-4;
}

code {
  @apply text-sm md:text-base !leading-loose;
}

pre > code {
  counter-reset: line;
}

code[data-theme*=" "],
code[data-theme*=" "] span {
  color: var(--shiki-light);
  background-color: var(--shiki-light-bg);
}

@media (prefers-color-scheme: dark) {
  code[data-theme*=" "],
  code[data-theme*=" "] span {
    color: var(--shiki-dark);
    background-color: var(--shiki-dark-bg);
  }
}

code[data-line-numbers] {
  counter-reset: line;
}

code[data-line-numbers] > [data-line]::before {
  counter-increment: line;
  content: counter(line);
  @apply inline-block w-4 mr-4 text-right text-gray-500;
}

code {
  counter-reset: line;
}

code > [data-line]::before {
  counter-increment: line;
  content: counter(line);

  /* Other styling */
  display: inline-block;
  width: 1rem;
  margin-right: 2rem;
  text-align: right;
  color: gray;
}

code[data-line-numbers-max-digits="2"] > [data-line]::before {
  width: 2rem;
}

code[data-line-numbers-max-digits="3"] > [data-line]::before {
  width: 3rem;
}
