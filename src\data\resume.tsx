import { Icons } from "@/components/icons";
import { HomeIcon } from "lucide-react";

export const DATA = {
  name: "<PERSON>",
  initials: "<PERSON>",
  url: "https://dillion.io",
  location: "San Francisco, CA",
  locationLink: "https://www.google.com/maps/place/sanfrancisco",
  description:
    "Business Information Systems | Banking & IT Intern | Eager Learner | Future Data Analyst | Backend Developer (Node.js)",
  summary:
    "I am a passionate Fullstack developer with expertise in modern web technologies. I enjoy creating responsive and user-friendly web applications using the latest technologies and best practices.",
  avatarUrl: "/me.png",
  skills: [
    "React",
    "JavaScript",
    "C#",
    "HTML",
    "CSS",
    "Node.js",
    "Express.js",
    "MongoDB",
    "Microsoft Office Suite",
  ],
  technicalSkills: {
    programming: ["Java (OOP)", "JavaScript", "C#", "HTML", "CSS"],
    webTechnologies: ["Node.js", "Express.js"],
    databases: ["MongoDB"],
    software: ["Microsoft Office Suite (Excel, Word, PowerPoint)"],
  },
  softSkills: [
    "Communication",
    "Negotiation",
    "Time Management",
    "Critical Thinking",
    "Teamwork",
    "Problem-Solving",
    "Analytical Skills",
  ],
  languages: [
    {
      language: "Arabic",
      level: "Native",
    },
    {
      language: "English",
      level: "Very Good",
    },
    {
      language: "French",
      level: "Intermediate",
    },
  ],
  navbar: [{ href: "/", icon: HomeIcon, label: "Home" }],
  contact: {
    email: "<EMAIL>",
    tel: "01018551242",
    social: {
      GitHub: {
        name: "GitHub",
        url: "https://github.com/mohamedayman517",
        icon: Icons.github,

        navbar: true,
      },
      LinkedIn: {
        name: "LinkedIn",
        url: "https://www.linkedin.com/in/mohamed-ayman-064b9230a/",
        icon: Icons.linkedin,

        navbar: true,
      },

      email: {
        name: "Send Email",
        url: "<EMAIL>",
        icon: Icons.email,

        navbar: false,
      },
    },
  },

  work: [
    {
      company: "Commercial International Bank (CIB)",
      href: "https://www.cibeg.com",
      badges: [],
      location: "Egypt",
      title: "Banking Intern",
      logoUrl: "/CIBCommon.png",
      start: "September 2024",
      end: "September 2024",
      description:
        "Assisted in analyzing financial statements for clients, contributing to banking process understanding. Developed foundational knowledge of credit risk assessment methodologies through practical application. Supported senior staff with data entry and report preparation.",
    },
    {
      company: "Egyptian Banking Institute",
      badges: [],
      href: "https://www.ebi.gov.eg",
      location: "Egypt",
      title: "Banking Trainee",
      logoUrl: "/EBI.png",
      start: "August 2024",
      end: "August 2024",
      description:
        "Completed intensive training modules covering core banking fundamentals and financial management principles. Gained exposure to industry best practices and regulatory requirements.",
    },
    {
      company: "Faisal Islamic Bank of Egypt",
      href: "https://www.faisalbank.com.eg",
      badges: [],
      location: "Egypt",
      title: "Banking Intern",
      logoUrl: "/FaisalIslamicBank.png",
      start: "August 2024",
      end: "August 2024",
      description:
        "Gained hands-on experience in processing diverse financial transactions and understanding Islamic banking operations. Assisted approximately 40+ customers daily, enhancing communication and client service skills.",
    },
    {
      company: "Faisal Islamic Bank of Egypt",
      href: "https://www.faisalbank.com.eg",
      badges: [],
      location: "Egypt",
      title: "Banking Intern",
      logoUrl: "/FaisalIslamicBank.png",
      start: "August 2023",
      end: "August 2023",
      description:
        "Observed and assisted with daily banking operations, including account opening procedures. Developed familiarity with financial transaction processing and customer interaction protocols. Processed over 100 financial transactions weekly with 98% accuracy. Reduced customer service response time by 20% by efficiently handling frequent requests.",
    },
    {
      company: "Programming Training",
      href: "#",
      badges: [],
      location: "Egypt",
      title: "Java Developer Trainee",
      logoUrl: "/ITI logo.png",
      start: "February 2023",
      end: "February 2023",
      description:
        "Acquired practical skills in Java programming, focusing on OOP concepts like encapsulation, inheritance, and polymorphism. Completed programming assignments applying OOP principles.",
    },
    {
      company: "Programming Training",
      href: "#",
      badges: [],
      location: "Egypt",
      title: "Programming Fundamentals Trainee",
      logoUrl: "/ITI logo.png",
      start: "February 2022",
      end: "February 2022",
      description:
        "Learned foundational programming concepts and logic building. Developed basic proficiency in C++ programming language.",
    },
  ],
  education: [
    {
      school: "Assiut University",
      href: "https://www.aun.edu.eg",
      degree: "Bachelor's Degree - Faculty of Commerce, BIS Program",
      logoUrl: "/university.png",
      start: "2021",
      end: "2025",
      description: "GPA: 3.707 (Excellent)",
    },
  ],
  projects: [
    {
      title: "WebDecor & More Project",
      href: "#",
      dates: "2025",
      active: true,
      description:
        "Developed a full-stack web application for an e-commerce platform focused on home decor. Developed a full-stack decor web app that handled product listings for 100+ items. Improved system response time by 40% using optimized backend queries in Node.js. Implemented features such as user authentication, product catalog, and shopping cart functionality.",
      technologies: [
        "Node.js",
        "MongoDB",
        "Express.js",
        "HTML",
        "CSS",
        "JavaScript",
      ],
      links: [
        {
          type: "Source",
          href: "https://github.com/mohamedayman517",
          icon: <Icons.github className="size-3" />,
        },
      ],
      image: "/decore.png",
      video: "",
    },
    {
      title: "Financial Analysis Report",
      href: "#",
      dates: "2024",
      active: true,
      description:
        "Conducted an in-depth analysis of market trends and investment opportunities within the Egyptian technology sector. Utilized Excel modeling to evaluate financial data and generate actionable insights.",
      technologies: [
        "Excel",
        "Financial Modeling",
        "Data Analysis",
        "Market Research",
      ],
      links: [],
      image: "",
      video: "",
    },
    {
      title: "Banking System Optimization",
      href: "#",
      dates: "2024",
      active: true,
      description:
        "Analyzed existing banking service workflows to identify bottlenecks and areas for improvement. Proposed specific enhancements aimed at increasing operational efficiency and customer satisfaction.",
      technologies: [
        "Process Analysis",
        "Workflow Optimization",
        "Banking Systems",
        "Business Analysis",
      ],
      links: [],
      image: "",
      video: "",
    },
  ],
} as const;
